# 🚀 TaskFlow Backend API

Backend API for TaskFlow - A professional task management application built with Node.js, Express, TypeScript, and MongoDB.

## ✨ Features

- 🔐 **JWT Authentication** - Secure user registration and login
- 📝 **Task CRUD Operations** - Create, read, update, delete tasks
- 🏷️ **Categories & Priorities** - Organize tasks with custom categories and priority levels
- 📅 **Due Dates** - Set and track task deadlines
- 🔍 **Advanced Filtering** - Filter tasks by status, category, priority
- 🛡️ **Security** - Helmet, CORS, rate limiting, password hashing
- 📊 **MongoDB Integration** - Persistent data storage with Mongoose

## 🛠️ Tech Stack

- **Node.js** with Express.js
- **TypeScript** for type safety
- **MongoDB** with Mongoose ODM
- **JWT** for authentication
- **bcrypt** for password hashing
- **Helmet** for security headers
- **CORS** for cross-origin requests
- **Express Rate Limit** for API protection

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- MongoDB (local or Atlas)
- npm or yarn

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/yourusername/taskflow-backend.git
   cd taskflow-backend
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment Setup**
   ```bash
   # Create .env file
   cp .env.example .env
   
   # Update .env with your values
   NODE_ENV=development
   PORT=5000
   MONGODB_URI=mongodb://localhost:27017/todolist
   JWT_SECRET=your-super-secret-jwt-key
   CLIENT_URL=http://localhost:5173
   ```

4. **Start the server**
   ```bash
   # Development
   npm run dev
   
   # Production
   npm run build
   npm start
   ```

## 📡 API Endpoints

### Authentication
- `POST /api/auth/register` - Register new user
- `POST /api/auth/login` - Login user
- `GET /api/auth/verify` - Verify JWT token

### Tasks
- `GET /api/tasks` - Get all user tasks
- `GET /api/tasks/:id` - Get single task
- `POST /api/tasks` - Create new task
- `PUT /api/tasks/:id` - Update task
- `DELETE /api/tasks/:id` - Delete task

### Health Check
- `GET /health` - Server health status

## 📦 Deployment to Vercel

### Environment Variables for Production

Set these in your Vercel dashboard:

```bash
NODE_ENV=production
MONGODB_URI=mongodb+srv://username:<EMAIL>/todolist
JWT_SECRET=your-production-jwt-secret-key
CLIENT_URL=https://your-frontend-url.vercel.app
```

### Deploy Steps

1. **Push to GitHub**
   ```bash
   git add .
   git commit -m "Deploy backend to Vercel"
   git push origin main
   ```

2. **Deploy to Vercel**
   - Go to [vercel.com](https://vercel.com)
   - Import your repository
   - Set environment variables
   - Deploy!

## 🔒 Security Features

- **JWT Authentication** - Secure token-based authentication
- **Password Hashing** - bcrypt with salt rounds
- **CORS Protection** - Configured for specific origins
- **Rate Limiting** - Prevents API abuse
- **Helmet** - Security headers
- **Input Validation** - Request validation and sanitization

## 📄 License

This project is licensed under the MIT License.

## 👨‍💻 Author

Built with ❤️ using modern backend technologies
