"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = __importDefault(require("mongoose"));
const connectDB = async () => {
    try {
        const mongoURI = process.env.MONGODB_URI || 'mongodb://localhost:27017/todolist';
        console.log('🔄 Connecting to MongoDB...');
        console.log('📍 MongoDB URI:', mongoURI.replace(/\/\/.*@/, '//***:***@'));
        await mongoose_1.default.connect(mongoURI);
        console.log('✅ MongoDB connected successfully');
    }
    catch (error) {
        console.error('❌ MongoDB connection error:', error);
        console.log('💡 Please ensure MongoDB is running or update MONGODB_URI in .env file');
        console.log('💡 For quick setup, you can use MongoDB Atlas: https://www.mongodb.com/atlas');
        process.exit(1);
    }
};
mongoose_1.default.connection.on('disconnected', () => {
    console.log('📡 MongoDB disconnected');
});
mongoose_1.default.connection.on('error', (error) => {
    console.error('❌ MongoDB error:', error);
});
process.on('SIGINT', async () => {
    await mongoose_1.default.connection.close();
    console.log('📡 MongoDB connection closed through app termination');
    process.exit(0);
});
exports.default = connectDB;
//# sourceMappingURL=database.js.map