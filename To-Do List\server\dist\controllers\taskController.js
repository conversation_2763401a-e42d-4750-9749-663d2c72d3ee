"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.deleteTask = exports.updateTask = exports.createTask = exports.getTask = exports.getTasks = void 0;
const Task_1 = __importDefault(require("../models/Task"));
const getTasks = async (req, res) => {
    try {
        const userId = req.user?._id;
        const { category, completed, priority, sortBy = 'createdAt', order = 'desc' } = req.query;
        const filter = { userId };
        if (category && category !== 'all') {
            filter.category = category;
        }
        if (completed !== undefined) {
            filter.completed = completed === 'true';
        }
        if (priority && priority !== 'all') {
            filter.priority = priority;
        }
        const sortOrder = order === 'asc' ? 1 : -1;
        const sortObj = {};
        sortObj[sortBy] = sortOrder;
        const tasks = await Task_1.default.find(filter).sort(sortObj);
        const transformedTasks = tasks.map(task => ({
            id: String(task._id),
            title: task.title,
            description: task.description,
            completed: task.completed,
            priority: task.priority,
            category: task.category,
            dueDate: task.dueDate?.toISOString(),
            createdAt: task.createdAt.toISOString(),
            updatedAt: task.updatedAt.toISOString(),
            userId: String(task.userId),
        }));
        res.status(200).json({
            success: true,
            count: transformedTasks.length,
            data: transformedTasks,
        });
    }
    catch (error) {
        console.error('Get tasks error:', error);
        res.status(500).json({
            success: false,
            message: 'Server error while fetching tasks',
        });
    }
};
exports.getTasks = getTasks;
const getTask = async (req, res) => {
    try {
        const userId = req.user?._id;
        if (!req.params.id || req.params.id === 'undefined') {
            res.status(400).json({
                success: false,
                message: 'Valid task ID is required',
            });
            return;
        }
        const task = await Task_1.default.findOne({ _id: req.params.id, userId });
        if (!task) {
            res.status(404).json({
                success: false,
                message: 'Task not found',
            });
            return;
        }
        const transformedTask = {
            id: String(task._id),
            title: task.title,
            description: task.description,
            completed: task.completed,
            priority: task.priority,
            category: task.category,
            dueDate: task.dueDate?.toISOString(),
            createdAt: task.createdAt.toISOString(),
            updatedAt: task.updatedAt.toISOString(),
            userId: String(task.userId),
        };
        res.status(200).json({
            success: true,
            data: transformedTask,
        });
    }
    catch (error) {
        console.error('Get task error:', error);
        res.status(500).json({
            success: false,
            message: 'Server error while fetching task',
        });
    }
};
exports.getTask = getTask;
const createTask = async (req, res) => {
    try {
        const userId = req.user?._id;
        const { title, description, priority, category, dueDate } = req.body;
        if (!title) {
            res.status(400).json({
                success: false,
                message: 'Task title is required',
            });
            return;
        }
        const task = await Task_1.default.create({
            title,
            description,
            priority: priority || 'medium',
            category: category || 'Personal',
            dueDate: dueDate ? new Date(dueDate) : undefined,
            userId,
        });
        const transformedTask = {
            id: String(task._id),
            title: task.title,
            description: task.description,
            completed: task.completed,
            priority: task.priority,
            category: task.category,
            dueDate: task.dueDate?.toISOString(),
            createdAt: task.createdAt.toISOString(),
            updatedAt: task.updatedAt.toISOString(),
            userId: String(task.userId),
        };
        res.status(201).json({
            success: true,
            message: 'Task created successfully',
            data: transformedTask,
        });
    }
    catch (error) {
        console.error('Create task error:', error);
        res.status(500).json({
            success: false,
            message: 'Server error while creating task',
        });
    }
};
exports.createTask = createTask;
const updateTask = async (req, res) => {
    try {
        const userId = req.user?._id;
        const { title, description, completed, priority, category, dueDate } = req.body;
        if (!req.params.id || req.params.id === 'undefined') {
            res.status(400).json({
                success: false,
                message: 'Valid task ID is required',
            });
            return;
        }
        const task = await Task_1.default.findOne({ _id: req.params.id, userId });
        if (!task) {
            res.status(404).json({
                success: false,
                message: 'Task not found',
            });
            return;
        }
        if (title !== undefined)
            task.title = title;
        if (description !== undefined)
            task.description = description;
        if (completed !== undefined)
            task.completed = completed;
        if (priority !== undefined)
            task.priority = priority;
        if (category !== undefined)
            task.category = category;
        if (dueDate !== undefined)
            task.dueDate = dueDate ? new Date(dueDate) : undefined;
        await task.save();
        const transformedTask = {
            id: String(task._id),
            title: task.title,
            description: task.description,
            completed: task.completed,
            priority: task.priority,
            category: task.category,
            dueDate: task.dueDate?.toISOString(),
            createdAt: task.createdAt.toISOString(),
            updatedAt: task.updatedAt.toISOString(),
            userId: String(task.userId),
        };
        res.status(200).json({
            success: true,
            message: 'Task updated successfully',
            data: transformedTask,
        });
    }
    catch (error) {
        console.error('Update task error:', error);
        res.status(500).json({
            success: false,
            message: 'Server error while updating task',
        });
    }
};
exports.updateTask = updateTask;
const deleteTask = async (req, res) => {
    try {
        const userId = req.user?._id;
        if (!req.params.id || req.params.id === 'undefined') {
            res.status(400).json({
                success: false,
                message: 'Valid task ID is required',
            });
            return;
        }
        const task = await Task_1.default.findOne({ _id: req.params.id, userId });
        if (!task) {
            res.status(404).json({
                success: false,
                message: 'Task not found',
            });
            return;
        }
        await Task_1.default.findByIdAndDelete(req.params.id);
        res.status(200).json({
            success: true,
            message: 'Task deleted successfully',
        });
    }
    catch (error) {
        console.error('Delete task error:', error);
        res.status(500).json({
            success: false,
            message: 'Server error while deleting task',
        });
    }
};
exports.deleteTask = deleteTask;
//# sourceMappingURL=taskController.js.map