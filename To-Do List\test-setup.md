# 🚀 TaskFlow - Quick Setup Guide

## Current Status
✅ Frontend running on: http://localhost:5173
⏳ Backend needs MongoDB connection

## Quick MongoDB Setup Options

### Option 1: MongoDB Atlas (Recommended - 5 minutes)
1. Visit: https://www.mongodb.com/atlas
2. Create free account and cluster
3. Create database user (Database Access)
4. Whitelist IP (Network Access → 0.0.0.0/0 for testing)
5. Get connection string (Clusters → Connect → Connect your application)
6. Update `server/.env` file:
   ```
   MONGODB_URI=your-actual-connection-string-here
   ```

### Option 2: Docker (If you have Docker)
```bash
cd "To-Do List"
docker-compose up -d
```

### Option 3: Local MongoDB
Download from: https://www.mongodb.com/try/download/community

## Testing the Application

### Frontend Features You Can See:
- ✅ Professional login/register pages
- ✅ Responsive design
- ✅ Smooth animations
- ✅ Modern UI with your preferred colors

### Once Backend is Connected:
- ✅ User registration and login
- ✅ Task creation, editing, deletion
- ✅ Task filtering and search
- ✅ Priority and category management
- ✅ Due date tracking

## Next Steps
1. Set up MongoDB (choose one option above)
2. Restart backend server: `cd server && npm run dev`
3. Test full application functionality

## Troubleshooting
- Frontend issues: Check http://localhost:5173
- Backend issues: Check server console output
- Database issues: Verify MongoDB connection string

## Application Features
- 🎨 Professional UI with animations
- 📱 Fully responsive design
- 🔐 Secure JWT authentication
- 📋 Complete task management
- 🔍 Advanced filtering and search
- 🎯 Priority and category system
